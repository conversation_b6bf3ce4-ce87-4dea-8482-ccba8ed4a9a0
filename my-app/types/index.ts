export interface Teacher {
  id: string;
  name: string;
  subject: string;
  department?: string;
  phone: string;
  email: string;
  office?: string;
  officeHours?: string;
}

export interface PDFDocument {
  id: string;
  name: string;
  uri: string;
  category: string;
  subject?: string;
  dateAdded: Date;
  size: number;
  bookmarks: PDFBookmark[];
  annotations: PDFAnnotation[];
}

export interface PDFBookmark {
  id: string;
  page: number;
  title: string;
  dateCreated: Date;
}

export interface PDFAnnotation {
  id: string;
  page: number;
  text: string;
  x: number;
  y: number;
  dateCreated: Date;
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  dueDate: Date;
  subject?: string;
  priority: 'low' | 'medium' | 'high';
  completed: boolean;
  dateCreated: Date;
}

export interface StudySession {
  id: string;
  type: 'pomodoro' | 'break';
  duration: number; // in minutes
  startTime: Date;
  endTime?: Date;
  subject?: string;
  completed: boolean;
}

export interface Note {
  id: string;
  title: string;
  content: string;
  subject?: string;
  dateCreated: Date;
  dateModified: Date;
}

export interface StudyStats {
  totalStudyTime: number; // in minutes
  sessionsCompleted: number;
  tasksCompleted: number;
  weeklyGoal: number;
  dailyStreak: number;
}

export type ThemeMode = 'light' | 'dark';

export interface AppTheme {
  mode: ThemeMode;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
    accent: string;
  };
}
