import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { StudySession, StudyStats } from '../../types';

interface PomodoroState {
  currentSession: StudySession | null;
  sessions: StudySession[];
  stats: StudyStats;
  isRunning: boolean;
  timeRemaining: number; // in seconds
  settings: {
    pomodoroLength: number; // in minutes
    shortBreakLength: number;
    longBreakLength: number;
    longBreakInterval: number;
    autoStartBreaks: boolean;
    autoStartPomodoros: boolean;
    soundEnabled: boolean;
  };
}

const initialState: PomodoroState = {
  currentSession: null,
  sessions: [],
  stats: {
    totalStudyTime: 0,
    sessionsCompleted: 0,
    tasksCompleted: 0,
    weeklyGoal: 1500, // 25 hours in minutes
    dailyStreak: 0,
  },
  isRunning: false,
  timeRemaining: 0,
  settings: {
    pomodoroLength: 25,
    shortBreakLength: 5,
    longBreakLength: 15,
    longBreakInterval: 4,
    autoStartBreaks: false,
    autoStartPomodoros: false,
    soundEnabled: true,
  },
};

const pomodoroSlice = createSlice({
  name: 'pomodoro',
  initialState,
  reducers: {
    startSession: (state, action: PayloadAction<{ type: 'pomodoro' | 'break'; duration: number; subject?: string }>) => {
      const session: StudySession = {
        id: Date.now().toString(),
        type: action.payload.type,
        duration: action.payload.duration,
        startTime: new Date(),
        subject: action.payload.subject,
        completed: false,
      };
      state.currentSession = session;
      state.isRunning = true;
      state.timeRemaining = action.payload.duration * 60; // convert to seconds
    },
    pauseSession: (state) => {
      state.isRunning = false;
    },
    resumeSession: (state) => {
      state.isRunning = true;
    },
    stopSession: (state) => {
      if (state.currentSession) {
        state.currentSession.endTime = new Date();
        state.sessions.push(state.currentSession);
      }
      state.currentSession = null;
      state.isRunning = false;
      state.timeRemaining = 0;
    },
    completeSession: (state) => {
      if (state.currentSession) {
        state.currentSession.endTime = new Date();
        state.currentSession.completed = true;
        state.sessions.push(state.currentSession);
        
        // Update stats
        if (state.currentSession.type === 'pomodoro') {
          state.stats.sessionsCompleted += 1;
          state.stats.totalStudyTime += state.currentSession.duration;
        }
      }
      state.currentSession = null;
      state.isRunning = false;
      state.timeRemaining = 0;
    },
    updateTimeRemaining: (state, action: PayloadAction<number>) => {
      state.timeRemaining = action.payload;
    },
    updateSettings: (state, action: PayloadAction<Partial<PomodoroState['settings']>>) => {
      state.settings = { ...state.settings, ...action.payload };
    },
    updateStats: (state, action: PayloadAction<Partial<StudyStats>>) => {
      state.stats = { ...state.stats, ...action.payload };
    },
    setSessions: (state, action: PayloadAction<StudySession[]>) => {
      state.sessions = action.payload;
    },
  },
});

export const {
  startSession,
  pauseSession,
  resumeSession,
  stopSession,
  completeSession,
  updateTimeRemaining,
  updateSettings,
  updateStats,
  setSessions,
} = pomodoroSlice.actions;

export default pomodoroSlice.reducer;
