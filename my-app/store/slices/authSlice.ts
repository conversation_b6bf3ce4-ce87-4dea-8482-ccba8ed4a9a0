import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface AuthState {
  isAuthenticated: boolean;
  isLocked: boolean;
  hasPin: boolean;
  biometricEnabled: boolean;
  lockTimeout: number; // in minutes
  lastActiveTime: number;
  failedAttempts: number;
  isBlocked: boolean;
  blockUntil: number | null;
}

const initialState: AuthState = {
  isAuthenticated: false,
  isLocked: true,
  hasPin: false,
  biometricEnabled: false,
  lockTimeout: 5, // 5 minutes
  lastActiveTime: Date.now(),
  failedAttempts: 0,
  isBlocked: false,
  blockUntil: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    authenticate: (state) => {
      state.isAuthenticated = true;
      state.isLocked = false;
      state.failedAttempts = 0;
      state.isBlocked = false;
      state.blockUntil = null;
      state.lastActiveTime = Date.now();
    },
    lock: (state) => {
      state.isAuthenticated = false;
      state.isLocked = true;
    },
    setPin: (state, action: PayloadAction<boolean>) => {
      state.hasPin = action.payload;
    },
    setBiometric: (state, action: PayloadAction<boolean>) => {
      state.biometricEnabled = action.payload;
    },
    setLockTimeout: (state, action: PayloadAction<number>) => {
      state.lockTimeout = action.payload;
    },
    updateLastActiveTime: (state) => {
      state.lastActiveTime = Date.now();
    },
    incrementFailedAttempts: (state) => {
      state.failedAttempts += 1;
      if (state.failedAttempts >= 5) {
        state.isBlocked = true;
        state.blockUntil = Date.now() + (30 * 60 * 1000); // Block for 30 minutes
      }
    },
    resetFailedAttempts: (state) => {
      state.failedAttempts = 0;
      state.isBlocked = false;
      state.blockUntil = null;
    },
    checkBlockStatus: (state) => {
      if (state.blockUntil && Date.now() > state.blockUntil) {
        state.isBlocked = false;
        state.blockUntil = null;
        state.failedAttempts = 0;
      }
    },
  },
});

export const {
  authenticate,
  lock,
  setPin,
  setBiometric,
  setLockTimeout,
  updateLastActiveTime,
  incrementFailedAttempts,
  resetFailedAttempts,
  checkBlockStatus,
} = authSlice.actions;

export default authSlice.reducer;
