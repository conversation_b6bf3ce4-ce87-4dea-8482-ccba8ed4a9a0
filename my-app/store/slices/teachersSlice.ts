import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Teacher } from '../../types';

interface TeachersState {
  teachers: Teacher[];
  loading: boolean;
  error: string | null;
  searchQuery: string;
  selectedSubject: string | null;
}

const initialState: TeachersState = {
  teachers: [],
  loading: false,
  error: null,
  searchQuery: '',
  selectedSubject: null,
};

const teachersSlice = createSlice({
  name: 'teachers',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    addTeacher: (state, action: PayloadAction<Teacher>) => {
      state.teachers.push(action.payload);
    },
    updateTeacher: (state, action: PayloadAction<Teacher>) => {
      const index = state.teachers.findIndex(t => t.id === action.payload.id);
      if (index !== -1) {
        state.teachers[index] = action.payload;
      }
    },
    deleteTeacher: (state, action: PayloadAction<string>) => {
      state.teachers = state.teachers.filter(t => t.id !== action.payload);
    },
    setTeachers: (state, action: PayloadAction<Teacher[]>) => {
      state.teachers = action.payload;
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    setSelectedSubject: (state, action: PayloadAction<string | null>) => {
      state.selectedSubject = action.payload;
    },
  },
});

export const {
  setLoading,
  setError,
  addTeacher,
  updateTeacher,
  deleteTeacher,
  setTeachers,
  setSearchQuery,
  setSelectedSubject,
} = teachersSlice.actions;

export default teachersSlice.reducer;
