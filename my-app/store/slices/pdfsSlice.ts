import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { PDFDocument, PDFBookmark, PDFAnnotation } from '../../types';

interface PDFsState {
  documents: PDFDocument[];
  loading: boolean;
  error: string | null;
  searchQuery: string;
  selectedCategory: string | null;
  currentDocument: PDFDocument | null;
}

const initialState: PDFsState = {
  documents: [],
  loading: false,
  error: null,
  searchQuery: '',
  selectedCategory: null,
  currentDocument: null,
};

const pdfsSlice = createSlice({
  name: 'pdfs',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    addDocument: (state, action: PayloadAction<PDFDocument>) => {
      state.documents.push(action.payload);
    },
    updateDocument: (state, action: PayloadAction<PDFDocument>) => {
      const index = state.documents.findIndex(d => d.id === action.payload.id);
      if (index !== -1) {
        state.documents[index] = action.payload;
      }
    },
    deleteDocument: (state, action: PayloadAction<string>) => {
      state.documents = state.documents.filter(d => d.id !== action.payload);
    },
    setDocuments: (state, action: PayloadAction<PDFDocument[]>) => {
      state.documents = action.payload;
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    setSelectedCategory: (state, action: PayloadAction<string | null>) => {
      state.selectedCategory = action.payload;
    },
    setCurrentDocument: (state, action: PayloadAction<PDFDocument | null>) => {
      state.currentDocument = action.payload;
    },
    addBookmark: (state, action: PayloadAction<{ documentId: string; bookmark: PDFBookmark }>) => {
      const document = state.documents.find(d => d.id === action.payload.documentId);
      if (document) {
        document.bookmarks.push(action.payload.bookmark);
      }
    },
    removeBookmark: (state, action: PayloadAction<{ documentId: string; bookmarkId: string }>) => {
      const document = state.documents.find(d => d.id === action.payload.documentId);
      if (document) {
        document.bookmarks = document.bookmarks.filter(b => b.id !== action.payload.bookmarkId);
      }
    },
    addAnnotation: (state, action: PayloadAction<{ documentId: string; annotation: PDFAnnotation }>) => {
      const document = state.documents.find(d => d.id === action.payload.documentId);
      if (document) {
        document.annotations.push(action.payload.annotation);
      }
    },
    removeAnnotation: (state, action: PayloadAction<{ documentId: string; annotationId: string }>) => {
      const document = state.documents.find(d => d.id === action.payload.documentId);
      if (document) {
        document.annotations = document.annotations.filter(a => a.id !== action.payload.annotationId);
      }
    },
  },
});

export const {
  setLoading,
  setError,
  addDocument,
  updateDocument,
  deleteDocument,
  setDocuments,
  setSearchQuery,
  setSelectedCategory,
  setCurrentDocument,
  addBookmark,
  removeBookmark,
  addAnnotation,
  removeAnnotation,
} = pdfsSlice.actions;

export default pdfsSlice.reducer;
