import { configureStore } from '@reduxjs/toolkit';
import teachersReducer from './slices/teachersSlice';
import pdfsReducer from './slices/pdfsSlice';
import tasksReducer from './slices/tasksSlice';
import pomodoroReducer from './slices/pomodoroSlice';
import notesReducer from './slices/notesSlice';
import authReducer from './slices/authSlice';
import { createPersistenceMiddleware } from '../utils/secureStorage';

export const store = configureStore({
  reducer: {
    teachers: teachersReducer,
    pdfs: pdfsReducer,
    tasks: tasksReducer,
    pomodoro: pomodoroReducer,
    notes: notesReducer,
    auth: authReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        ignoredPaths: ['pomodoro.currentSession.startTime', 'pomodoro.currentSession.endTime'],
      },
    }).concat(createPersistenceMiddleware('app_data')),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
