import * as SecureStore from 'expo-secure-store';
import * as Crypto from 'expo-crypto';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Keys for different types of data
const KEYS = {
  USER_PIN: 'user_pin',
  ENCRYPTION_KEY: 'encryption_key',
  TEACHERS_DATA: 'teachers_data',
  PDFS_DATA: 'pdfs_data',
  TASKS_DATA: 'tasks_data',
  NOTES_DATA: 'notes_data',
  POMODORO_DATA: 'pomodoro_data',
  APP_SETTINGS: 'app_settings',
};

// Generate or retrieve encryption key
export const getEncryptionKey = async (): Promise<string> => {
  try {
    let key = await SecureStore.getItemAsync(KEYS.ENCRYPTION_KEY);
    if (!key) {
      // Generate a new encryption key
      key = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        Date.now().toString() + Math.random().toString(),
        { encoding: Crypto.CryptoEncoding.HEX }
      );
      await SecureStore.setItemAsync(KEYS.ENCRYPTION_KEY, key);
    }
    return key;
  } catch (error) {
    console.error('Error getting encryption key:', error);
    throw error;
  }
};

// Simple encryption using XOR cipher (for demo purposes)
// In production, use a proper encryption library like react-native-crypto-js
const simpleEncrypt = (text: string, key: string): string => {
  let encrypted = '';
  for (let i = 0; i < text.length; i++) {
    encrypted += String.fromCharCode(
      text.charCodeAt(i) ^ key.charCodeAt(i % key.length)
    );
  }
  return btoa(encrypted); // Base64 encode
};

const simpleDecrypt = (encryptedText: string, key: string): string => {
  try {
    const decoded = atob(encryptedText); // Base64 decode
    let decrypted = '';
    for (let i = 0; i < decoded.length; i++) {
      decrypted += String.fromCharCode(
        decoded.charCodeAt(i) ^ key.charCodeAt(i % key.length)
      );
    }
    return decrypted;
  } catch (error) {
    console.error('Decryption error:', error);
    return '';
  }
};

// Secure storage functions
export const secureStorage = {
  // Store encrypted data
  setItem: async (key: string, value: any): Promise<void> => {
    try {
      const encryptionKey = await getEncryptionKey();
      const jsonString = JSON.stringify(value);
      const encryptedData = simpleEncrypt(jsonString, encryptionKey);
      
      // Use SecureStore for sensitive data, AsyncStorage for less sensitive data
      if (key.includes('pin') || key.includes('key') || key.includes('auth')) {
        await SecureStore.setItemAsync(key, encryptedData);
      } else {
        await AsyncStorage.setItem(key, encryptedData);
      }
    } catch (error) {
      console.error('Error storing secure data:', error);
      throw error;
    }
  },

  // Retrieve and decrypt data
  getItem: async (key: string): Promise<any> => {
    try {
      const encryptionKey = await getEncryptionKey();
      let encryptedData: string | null;
      
      // Try SecureStore first, then AsyncStorage
      if (key.includes('pin') || key.includes('key') || key.includes('auth')) {
        encryptedData = await SecureStore.getItemAsync(key);
      } else {
        encryptedData = await AsyncStorage.getItem(key);
      }
      
      if (!encryptedData) return null;
      
      const decryptedData = simpleDecrypt(encryptedData, encryptionKey);
      return JSON.parse(decryptedData);
    } catch (error) {
      console.error('Error retrieving secure data:', error);
      return null;
    }
  },

  // Remove data
  removeItem: async (key: string): Promise<void> => {
    try {
      if (key.includes('pin') || key.includes('key') || key.includes('auth')) {
        await SecureStore.deleteItemAsync(key);
      } else {
        await AsyncStorage.removeItem(key);
      }
    } catch (error) {
      console.error('Error removing secure data:', error);
      throw error;
    }
  },

  // Clear all app data (for logout/reset)
  clearAll: async (): Promise<void> => {
    try {
      const allKeys = Object.values(KEYS);
      await Promise.all(
        allKeys.map(async (key) => {
          try {
            await secureStorage.removeItem(key);
          } catch (error) {
            // Ignore individual key errors
          }
        })
      );
    } catch (error) {
      console.error('Error clearing all data:', error);
      throw error;
    }
  },
};

// Specific storage functions for different data types
export const teachersStorage = {
  save: (teachers: any[]) => secureStorage.setItem(KEYS.TEACHERS_DATA, teachers),
  load: () => secureStorage.getItem(KEYS.TEACHERS_DATA),
  clear: () => secureStorage.removeItem(KEYS.TEACHERS_DATA),
};

export const pdfsStorage = {
  save: (pdfs: any[]) => secureStorage.setItem(KEYS.PDFS_DATA, pdfs),
  load: () => secureStorage.getItem(KEYS.PDFS_DATA),
  clear: () => secureStorage.removeItem(KEYS.PDFS_DATA),
};

export const tasksStorage = {
  save: (tasks: any[]) => secureStorage.setItem(KEYS.TASKS_DATA, tasks),
  load: () => secureStorage.getItem(KEYS.TASKS_DATA),
  clear: () => secureStorage.removeItem(KEYS.TASKS_DATA),
};

export const notesStorage = {
  save: (notes: any[]) => secureStorage.setItem(KEYS.NOTES_DATA, notes),
  load: () => secureStorage.getItem(KEYS.NOTES_DATA),
  clear: () => secureStorage.removeItem(KEYS.NOTES_DATA),
};

export const pomodoroStorage = {
  save: (data: any) => secureStorage.setItem(KEYS.POMODORO_DATA, data),
  load: () => secureStorage.getItem(KEYS.POMODORO_DATA),
  clear: () => secureStorage.removeItem(KEYS.POMODORO_DATA),
};

export const settingsStorage = {
  save: (settings: any) => secureStorage.setItem(KEYS.APP_SETTINGS, settings),
  load: () => secureStorage.getItem(KEYS.APP_SETTINGS),
  clear: () => secureStorage.removeItem(KEYS.APP_SETTINGS),
};

// PIN management
export const pinStorage = {
  set: async (pin: string): Promise<void> => {
    const hashedPin = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      pin,
      { encoding: Crypto.CryptoEncoding.HEX }
    );
    await SecureStore.setItemAsync(KEYS.USER_PIN, hashedPin);
  },

  verify: async (pin: string): Promise<boolean> => {
    try {
      const storedHash = await SecureStore.getItemAsync(KEYS.USER_PIN);
      if (!storedHash) return false;
      
      const inputHash = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        pin,
        { encoding: Crypto.CryptoEncoding.HEX }
      );
      
      return storedHash === inputHash;
    } catch (error) {
      console.error('PIN verification error:', error);
      return false;
    }
  },

  exists: async (): Promise<boolean> => {
    try {
      const pin = await SecureStore.getItemAsync(KEYS.USER_PIN);
      return !!pin;
    } catch (error) {
      return false;
    }
  },

  remove: async (): Promise<void> => {
    await SecureStore.deleteItemAsync(KEYS.USER_PIN);
  },
};

// Data persistence middleware for Redux
export const createPersistenceMiddleware = (storageKey: string) => {
  return (store: any) => (next: any) => (action: any) => {
    const result = next(action);
    
    // Save state after certain actions
    if (action.type.includes('add') || action.type.includes('update') || action.type.includes('delete')) {
      const state = store.getState();
      
      // Save relevant slice data based on action type
      if (action.type.includes('teachers')) {
        teachersStorage.save(state.teachers.teachers);
      } else if (action.type.includes('pdfs')) {
        pdfsStorage.save(state.pdfs.documents);
      } else if (action.type.includes('tasks')) {
        tasksStorage.save(state.tasks.tasks);
      } else if (action.type.includes('notes')) {
        notesStorage.save(state.notes.notes);
      } else if (action.type.includes('pomodoro')) {
        pomodoroStorage.save({
          sessions: state.pomodoro.sessions,
          stats: state.pomodoro.stats,
          settings: state.pomodoro.settings,
        });
      }
    }
    
    return result;
  };
};
