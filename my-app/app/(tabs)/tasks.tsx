import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Modal, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSelector, useDispatch } from 'react-redux';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTheme } from '../../contexts/ThemeContext';
import { RootState } from '../../store';
import { addTask, toggleTaskCompletion, setFilter } from '../../store/slices/tasksSlice';
import { Task } from '../../types';

export default function TasksScreen() {
  const { theme } = useTheme();
  const dispatch = useDispatch();
  const { tasks, filter } = useSelector((state: RootState) => state.tasks);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    dueDate: new Date(),
    subject: '',
    priority: 'medium' as 'low' | 'medium' | 'high',
  });

  const filters = [
    { key: 'all', label: 'All', icon: 'list' },
    { key: 'pending', label: 'Pending', icon: 'time' },
    { key: 'completed', label: 'Completed', icon: 'checkmark-circle' },
    { key: 'overdue', label: 'Overdue', icon: 'warning' },
  ];

  const priorities = [
    { key: 'low', label: 'Low', color: theme.colors.success },
    { key: 'medium', label: 'Medium', color: theme.colors.warning },
    { key: 'high', label: 'High', color: theme.colors.error },
  ];

  const filteredTasks = tasks.filter(task => {
    const now = new Date();
    const isOverdue = new Date(task.dueDate) < now && !task.completed;
    
    switch (filter) {
      case 'pending':
        return !task.completed;
      case 'completed':
        return task.completed;
      case 'overdue':
        return isOverdue;
      default:
        return true;
    }
  });

  const handleAddTask = () => {
    if (!newTask.title.trim()) {
      Alert.alert('Error', 'Please enter a task title');
      return;
    }

    const task: Task = {
      id: Date.now().toString(),
      title: newTask.title,
      description: newTask.description,
      dueDate: newTask.dueDate,
      subject: newTask.subject,
      priority: newTask.priority,
      completed: false,
      dateCreated: new Date(),
    };

    dispatch(addTask(task));
    setNewTask({
      title: '',
      description: '',
      dueDate: new Date(),
      subject: '',
      priority: 'medium',
    });
    setShowAddModal(false);
    Alert.alert('Success', 'Task added successfully!');
  };

  const handleToggleTask = (taskId: string) => {
    dispatch(toggleTaskCompletion(taskId));
  };

  const FilterChip = ({ filterItem }: { filterItem: any }) => (
    <TouchableOpacity
      style={[
        styles.filterChip,
        {
          backgroundColor: filter === filterItem.key ? theme.colors.primary : theme.colors.surface,
          borderColor: theme.colors.border,
        }
      ]}
      onPress={() => dispatch(setFilter(filterItem.key))}
    >
      <Ionicons 
        name={filterItem.icon} 
        size={16} 
        color={filter === filterItem.key ? '#FFFFFF' : theme.colors.textSecondary} 
      />
      <Text style={[
        styles.filterText,
        { color: filter === filterItem.key ? '#FFFFFF' : theme.colors.text }
      ]}>
        {filterItem.label}
      </Text>
    </TouchableOpacity>
  );

  const TaskCard = ({ task }: { task: Task }) => {
    const isOverdue = new Date(task.dueDate) < new Date() && !task.completed;
    const priorityColor = priorities.find(p => p.key === task.priority)?.color || theme.colors.textSecondary;

    return (
      <TouchableOpacity
        style={[
          styles.taskCard,
          {
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border,
            opacity: task.completed ? 0.7 : 1,
          }
        ]}
      >
        <TouchableOpacity
          style={styles.checkbox}
          onPress={() => handleToggleTask(task.id)}
        >
          <Ionicons
            name={task.completed ? "checkmark-circle" : "ellipse-outline"}
            size={24}
            color={task.completed ? theme.colors.success : theme.colors.textSecondary}
          />
        </TouchableOpacity>

        <View style={styles.taskContent}>
          <Text
            style={[
              styles.taskTitle,
              {
                color: theme.colors.text,
                textDecorationLine: task.completed ? 'line-through' : 'none',
              }
            ]}
          >
            {task.title}
          </Text>
          
          {task.description && (
            <Text style={[styles.taskDescription, { color: theme.colors.textSecondary }]}>
              {task.description}
            </Text>
          )}

          <View style={styles.taskMeta}>
            <View style={styles.taskMetaItem}>
              <Ionicons name="calendar" size={14} color={theme.colors.textSecondary} />
              <Text style={[styles.taskMetaText, { color: isOverdue ? theme.colors.error : theme.colors.textSecondary }]}>
                {new Date(task.dueDate).toLocaleDateString()}
              </Text>
            </View>
            
            {task.subject && (
              <View style={styles.taskMetaItem}>
                <Ionicons name="book" size={14} color={theme.colors.textSecondary} />
                <Text style={[styles.taskMetaText, { color: theme.colors.textSecondary }]}>
                  {task.subject}
                </Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.taskActions}>
          <View style={[styles.priorityBadge, { backgroundColor: priorityColor + '20' }]}>
            <Text style={[styles.priorityText, { color: priorityColor }]}>
              {task.priority.toUpperCase()}
            </Text>
          </View>
          {isOverdue && (
            <Ionicons name="warning" size={20} color={theme.colors.error} />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const PrioritySelector = () => (
    <View style={styles.priorityContainer}>
      <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Priority</Text>
      <View style={styles.priorityButtons}>
        {priorities.map((priority) => (
          <TouchableOpacity
            key={priority.key}
            style={[
              styles.priorityButton,
              {
                backgroundColor: newTask.priority === priority.key ? priority.color + '20' : theme.colors.surface,
                borderColor: newTask.priority === priority.key ? priority.color : theme.colors.border,
              }
            ]}
            onPress={() => setNewTask({ ...newTask, priority: priority.key as any })}
          >
            <Text style={[
              styles.priorityButtonText,
              { color: newTask.priority === priority.key ? priority.color : theme.colors.text }
            ]}>
              {priority.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Tasks</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => setShowAddModal(true)}
        >
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Filters */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersContainer}>
        {filters.map((filterItem) => (
          <FilterChip key={filterItem.key} filterItem={filterItem} />
        ))}
      </ScrollView>

      {/* Tasks List */}
      <ScrollView style={styles.tasksList}>
        {filteredTasks.map((task) => (
          <TaskCard key={task.id} task={task} />
        ))}
        {filteredTasks.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="checkbox-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
              {tasks.length === 0 ? 'No tasks yet' : 'No tasks match your filter'}
            </Text>
            <Text style={[styles.emptySubtext, { color: theme.colors.textSecondary }]}>
              {tasks.length === 0 ? 'Tap the + button to add your first task' : 'Try selecting a different filter'}
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Add Task Modal */}
      <Modal visible={showAddModal} animationType="slide" presentationStyle="pageSheet">
        <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <Text style={[styles.cancelButton, { color: theme.colors.primary }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Add Task</Text>
            <TouchableOpacity onPress={handleAddTask}>
              <Text style={[styles.saveButton, { color: theme.colors.primary }]}>Save</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Title *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border, color: theme.colors.text }]}
                value={newTask.title}
                onChangeText={(text) => setNewTask({ ...newTask, title: text })}
                placeholder="Enter task title"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Description</Text>
              <TextInput
                style={[styles.textArea, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border, color: theme.colors.text }]}
                value={newTask.description}
                onChangeText={(text) => setNewTask({ ...newTask, description: text })}
                placeholder="Enter task description"
                placeholderTextColor={theme.colors.textSecondary}
                multiline
                numberOfLines={3}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Subject</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border, color: theme.colors.text }]}
                value={newTask.subject}
                onChangeText={(text) => setNewTask({ ...newTask, subject: text })}
                placeholder="Enter subject (optional)"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Due Date</Text>
              <TouchableOpacity
                style={[styles.dateButton, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
                onPress={() => setShowDatePicker(true)}
              >
                <Ionicons name="calendar" size={20} color={theme.colors.textSecondary} />
                <Text style={[styles.dateText, { color: theme.colors.text }]}>
                  {newTask.dueDate.toLocaleDateString()}
                </Text>
              </TouchableOpacity>
            </View>

            <PrioritySelector />
          </ScrollView>

          {showDatePicker && (
            <DateTimePicker
              value={newTask.dueDate}
              mode="date"
              display="default"
              onChange={(event, selectedDate) => {
                setShowDatePicker(false);
                if (selectedDate) {
                  setNewTask({ ...newTask, dueDate: selectedDate });
                }
              }}
            />
          )}
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 50,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filtersContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 12,
    gap: 6,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '600',
  },
  tasksList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  taskCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
    alignItems: 'flex-start',
  },
  checkbox: {
    marginRight: 12,
    marginTop: 2,
  },
  taskContent: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  taskDescription: {
    fontSize: 14,
    marginBottom: 8,
    lineHeight: 20,
  },
  taskMeta: {
    flexDirection: 'row',
    gap: 16,
  },
  taskMetaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  taskMetaText: {
    fontSize: 12,
  },
  taskActions: {
    alignItems: 'flex-end',
    gap: 8,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 40,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  cancelButton: {
    fontSize: 16,
  },
  saveButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 16,
  },
  textArea: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 8,
  },
  dateText: {
    fontSize: 16,
  },
  priorityContainer: {
    marginBottom: 20,
  },
  priorityButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  priorityButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  priorityButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});
