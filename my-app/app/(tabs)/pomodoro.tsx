import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSelector, useDispatch } from 'react-redux';
import * as Haptics from 'expo-haptics';
import { useTheme } from '../../contexts/ThemeContext';
import { RootState } from '../../store';
import { 
  startSession, 
  pauseSession, 
  resumeSession, 
  stopSession, 
  completeSession, 
  updateTimeRemaining 
} from '../../store/slices/pomodoroSlice';

const { width } = Dimensions.get('window');

export default function PomodoroScreen() {
  const { theme } = useTheme();
  const dispatch = useDispatch();
  const { currentSession, isRunning, timeRemaining, settings, stats } = useSelector(
    (state: RootState) => state.pomodoro
  );
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [selectedDuration, setSelectedDuration] = useState(25);

  const durations = [
    { label: '25 min', value: 25, type: 'pomodoro' as const },
    { label: '50 min', value: 50, type: 'pomodoro' as const },
    { label: '90 min', value: 90, type: 'pomodoro' as const },
  ];

  const breakDurations = [
    { label: '5 min', value: 5, type: 'break' as const },
    { label: '10 min', value: 10, type: 'break' as const },
    { label: '15 min', value: 15, type: 'break' as const },
  ];

  useEffect(() => {
    if (isRunning && timeRemaining > 0) {
      intervalRef.current = setInterval(() => {
        dispatch(updateTimeRemaining(timeRemaining - 1));
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      
      if (timeRemaining === 0 && currentSession) {
        handleSessionComplete();
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, timeRemaining]);

  const handleSessionComplete = () => {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    dispatch(completeSession());
    
    const sessionType = currentSession?.type === 'pomodoro' ? 'Study' : 'Break';
    Alert.alert(
      'Session Complete!',
      `${sessionType} session completed successfully.`,
      [{ text: 'OK', onPress: () => {} }]
    );
  };

  const handleStart = () => {
    if (currentSession) {
      dispatch(resumeSession());
    } else {
      dispatch(startSession({
        type: 'pomodoro',
        duration: selectedDuration,
      }));
    }
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  const handlePause = () => {
    dispatch(pauseSession());
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleStop = () => {
    Alert.alert(
      'Stop Session',
      'Are you sure you want to stop the current session?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Stop', 
          style: 'destructive',
          onPress: () => {
            dispatch(stopSession());
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          }
        }
      ]
    );
  };

  const handleStartBreak = (duration: number) => {
    dispatch(startSession({
      type: 'break',
      duration: duration,
    }));
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const progress = currentSession ? 
    (currentSession.duration * 60 - timeRemaining) / (currentSession.duration * 60) : 0;

  const CircularProgress = ({ progress, size = 280 }: { progress: number; size?: number }) => {
    const strokeWidth = 8;
    const radius = (size - strokeWidth) / 2;
    const circumference = radius * 2 * Math.PI;
    const strokeDashoffset = circumference - (progress * circumference);

    return (
      <View style={{ width: size, height: size }}>
        <svg width={size} height={size} style={{ position: 'absolute' }}>
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={theme.colors.border}
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={currentSession?.type === 'break' ? theme.colors.success : theme.colors.primary}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            transform={`rotate(-90 ${size / 2} ${size / 2})`}
          />
        </svg>
      </View>
    );
  };

  const DurationButton = ({ duration, isSelected, onPress }: any) => (
    <TouchableOpacity
      style={[
        styles.durationButton,
        {
          backgroundColor: isSelected ? theme.colors.primary : theme.colors.surface,
          borderColor: theme.colors.border,
        }
      ]}
      onPress={onPress}
    >
      <Text style={[
        styles.durationText,
        { color: isSelected ? '#FFFFFF' : theme.colors.text }
      ]}>
        {duration.label}
      </Text>
    </TouchableOpacity>
  );

  const StatCard = ({ title, value, icon }: any) => (
    <View style={[styles.statCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}>
      <Ionicons name={icon} size={24} color={theme.colors.primary} />
      <Text style={[styles.statValue, { color: theme.colors.text }]}>{value}</Text>
      <Text style={[styles.statTitle, { color: theme.colors.textSecondary }]}>{title}</Text>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Focus Timer</Text>
        <TouchableOpacity style={styles.settingsButton}>
          <Ionicons name="settings-outline" size={24} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>

      {/* Timer Circle */}
      <View style={styles.timerContainer}>
        <View style={styles.circularTimer}>
          <CircularProgress progress={progress} />
          <View style={styles.timerContent}>
            <Text style={[styles.timerText, { color: theme.colors.text }]}>
              {formatTime(timeRemaining)}
            </Text>
            <Text style={[styles.sessionType, { color: theme.colors.textSecondary }]}>
              {currentSession ? (currentSession.type === 'pomodoro' ? 'Focus' : 'Break') : 'Ready'}
            </Text>
          </View>
        </View>
      </View>

      {/* Controls */}
      <View style={styles.controls}>
        {!currentSession ? (
          <>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Study Duration</Text>
            <View style={styles.durationContainer}>
              {durations.map((duration) => (
                <DurationButton
                  key={duration.value}
                  duration={duration}
                  isSelected={selectedDuration === duration.value}
                  onPress={() => setSelectedDuration(duration.value)}
                />
              ))}
            </View>
            <TouchableOpacity
              style={[styles.startButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleStart}
            >
              <Ionicons name="play" size={32} color="#FFFFFF" />
              <Text style={styles.startButtonText}>Start Focus</Text>
            </TouchableOpacity>
          </>
        ) : (
          <View style={styles.activeControls}>
            <View style={styles.controlButtons}>
              <TouchableOpacity
                style={[styles.controlButton, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
                onPress={handleStop}
              >
                <Ionicons name="stop" size={24} color={theme.colors.error} />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.mainControlButton, { backgroundColor: isRunning ? theme.colors.warning : theme.colors.primary }]}
                onPress={isRunning ? handlePause : handleStart}
              >
                <Ionicons name={isRunning ? "pause" : "play"} size={32} color="#FFFFFF" />
              </TouchableOpacity>
            </View>

            {currentSession.type === 'pomodoro' && (
              <>
                <Text style={[styles.sectionTitle, { color: theme.colors.text, marginTop: 20 }]}>Quick Break</Text>
                <View style={styles.breakContainer}>
                  {breakDurations.map((duration) => (
                    <TouchableOpacity
                      key={duration.value}
                      style={[styles.breakButton, { backgroundColor: theme.colors.success + '20', borderColor: theme.colors.success }]}
                      onPress={() => handleStartBreak(duration.value)}
                    >
                      <Text style={[styles.breakButtonText, { color: theme.colors.success }]}>
                        {duration.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </>
            )}
          </View>
        )}
      </View>

      {/* Stats */}
      <View style={styles.statsContainer}>
        <StatCard 
          title="Today" 
          value={`${Math.floor(stats.totalStudyTime / 60)}h ${stats.totalStudyTime % 60}m`} 
          icon="time" 
        />
        <StatCard 
          title="Sessions" 
          value={stats.sessionsCompleted} 
          icon="checkmark-circle" 
        />
        <StatCard 
          title="Streak" 
          value={`${stats.dailyStreak} days`} 
          icon="flame" 
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 50,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  settingsButton: {
    padding: 8,
  },
  timerContainer: {
    alignItems: 'center',
    marginVertical: 40,
  },
  circularTimer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  timerContent: {
    position: 'absolute',
    alignItems: 'center',
  },
  timerText: {
    fontSize: 48,
    fontWeight: 'bold',
    fontFamily: 'monospace',
  },
  sessionType: {
    fontSize: 16,
    marginTop: 8,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  controls: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  durationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 30,
  },
  durationButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    borderWidth: 1,
    minWidth: 80,
    alignItems: 'center',
  },
  durationText: {
    fontSize: 16,
    fontWeight: '600',
  },
  startButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 30,
    gap: 12,
  },
  startButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  activeControls: {
    alignItems: 'center',
  },
  controlButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mainControlButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  breakContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 12,
  },
  breakButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    flex: 1,
    alignItems: 'center',
  },
  breakButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
  },
  statCard: {
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    minWidth: 80,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 4,
  },
  statTitle: {
    fontSize: 12,
    textAlign: 'center',
  },
});
