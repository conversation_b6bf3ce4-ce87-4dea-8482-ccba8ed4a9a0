import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Modal, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSelector, useDispatch } from 'react-redux';
import { useTheme } from '../../contexts/ThemeContext';
import { RootState } from '../../store';
import { addTeacher, setSearchQuery, setSelectedSubject } from '../../store/slices/teachersSlice';
import { Teacher } from '../../types';

export default function TeachersScreen() {
  const { theme } = useTheme();
  const dispatch = useDispatch();
  const { teachers, searchQuery, selectedSubject } = useSelector((state: RootState) => state.teachers);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newTeacher, setNewTeacher] = useState({
    name: '',
    subject: '',
    department: '',
    phone: '',
    email: '',
    office: '',
    officeHours: '',
  });

  const subjects = ['All', 'Mathematics', 'Physics', 'Chemistry', 'Biology', 'Computer Science', 'English', 'History', 'Other'];

  const filteredTeachers = teachers.filter(teacher => {
    const matchesSearch = teacher.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         teacher.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         teacher.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesSubject = !selectedSubject || selectedSubject === 'All' || teacher.subject === selectedSubject;
    return matchesSearch && matchesSubject;
  });

  const handleAddTeacher = () => {
    if (!newTeacher.name || !newTeacher.subject || !newTeacher.email) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    const teacher: Teacher = {
      id: Date.now().toString(),
      ...newTeacher,
    };

    dispatch(addTeacher(teacher));
    setNewTeacher({
      name: '',
      subject: '',
      department: '',
      phone: '',
      email: '',
      office: '',
      officeHours: '',
    });
    setShowAddModal(false);
    Alert.alert('Success', 'Teacher added successfully!');
  };

  const SubjectChip = ({ subject }: { subject: string }) => (
    <TouchableOpacity
      style={[
        styles.subjectChip,
        {
          backgroundColor: selectedSubject === subject ? theme.colors.primary : theme.colors.surface,
          borderColor: theme.colors.border,
        }
      ]}
      onPress={() => dispatch(setSelectedSubject(subject === 'All' ? null : subject))}
    >
      <Text style={[
        styles.subjectText,
        { color: selectedSubject === subject ? '#FFFFFF' : theme.colors.text }
      ]}>
        {subject}
      </Text>
    </TouchableOpacity>
  );

  const TeacherCard = ({ teacher }: { teacher: Teacher }) => (
    <TouchableOpacity
      style={[styles.teacherCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
    >
      <View style={[styles.avatar, { backgroundColor: theme.colors.primary + '20' }]}>
        <Text style={[styles.avatarText, { color: theme.colors.primary }]}>
          {teacher.name.split(' ').map(n => n[0]).join('').toUpperCase()}
        </Text>
      </View>
      <View style={styles.teacherInfo}>
        <Text style={[styles.teacherName, { color: theme.colors.text }]}>{teacher.name}</Text>
        <Text style={[styles.teacherSubject, { color: theme.colors.primary }]}>{teacher.subject}</Text>
        {teacher.department && (
          <Text style={[styles.teacherDepartment, { color: theme.colors.textSecondary }]}>
            {teacher.department}
          </Text>
        )}
        <View style={styles.contactInfo}>
          <View style={styles.contactItem}>
            <Ionicons name="mail" size={14} color={theme.colors.textSecondary} />
            <Text style={[styles.contactText, { color: theme.colors.textSecondary }]}>{teacher.email}</Text>
          </View>
          {teacher.phone && (
            <View style={styles.contactItem}>
              <Ionicons name="call" size={14} color={theme.colors.textSecondary} />
              <Text style={[styles.contactText, { color: theme.colors.textSecondary }]}>{teacher.phone}</Text>
            </View>
          )}
        </View>
      </View>
      <View style={styles.actions}>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="call" size={20} color={theme.colors.primary} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="mail" size={20} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Teachers</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => setShowAddModal(true)}
        >
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <View style={[styles.searchBox, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}>
          <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            placeholder="Search teachers..."
            placeholderTextColor={theme.colors.textSecondary}
            value={searchQuery}
            onChangeText={(text) => dispatch(setSearchQuery(text))}
          />
        </View>
      </View>

      {/* Subjects */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.subjectsContainer}>
        {subjects.map((subject) => (
          <SubjectChip key={subject} subject={subject} />
        ))}
      </ScrollView>

      {/* Teachers List */}
      <ScrollView style={styles.teachersList}>
        {filteredTeachers.map((teacher) => (
          <TeacherCard key={teacher.id} teacher={teacher} />
        ))}
        {filteredTeachers.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="people-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
              {teachers.length === 0 ? 'No teachers yet' : 'No teachers match your search'}
            </Text>
            <Text style={[styles.emptySubtext, { color: theme.colors.textSecondary }]}>
              {teachers.length === 0 ? 'Tap the + button to add your first teacher' : 'Try adjusting your search or filters'}
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Add Teacher Modal */}
      <Modal visible={showAddModal} animationType="slide" presentationStyle="pageSheet">
        <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <Text style={[styles.cancelButton, { color: theme.colors.primary }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Add Teacher</Text>
            <TouchableOpacity onPress={handleAddTeacher}>
              <Text style={[styles.saveButton, { color: theme.colors.primary }]}>Save</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Name *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border, color: theme.colors.text }]}
                value={newTeacher.name}
                onChangeText={(text) => setNewTeacher({ ...newTeacher, name: text })}
                placeholder="Enter teacher's name"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Subject *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border, color: theme.colors.text }]}
                value={newTeacher.subject}
                onChangeText={(text) => setNewTeacher({ ...newTeacher, subject: text })}
                placeholder="Enter subject"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Email *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border, color: theme.colors.text }]}
                value={newTeacher.email}
                onChangeText={(text) => setNewTeacher({ ...newTeacher, email: text })}
                placeholder="Enter email address"
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Phone</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border, color: theme.colors.text }]}
                value={newTeacher.phone}
                onChangeText={(text) => setNewTeacher({ ...newTeacher, phone: text })}
                placeholder="Enter phone number"
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="phone-pad"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Department</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border, color: theme.colors.text }]}
                value={newTeacher.department}
                onChangeText={(text) => setNewTeacher({ ...newTeacher, department: text })}
                placeholder="Enter department"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Office</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border, color: theme.colors.text }]}
                value={newTeacher.office}
                onChangeText={(text) => setNewTeacher({ ...newTeacher, office: text })}
                placeholder="Enter office location"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Office Hours</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border, color: theme.colors.text }]}
                value={newTeacher.officeHours}
                onChangeText={(text) => setNewTeacher({ ...newTeacher, officeHours: text })}
                placeholder="e.g., Mon-Fri 2-4 PM"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 50,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  subjectsContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  subjectChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 12,
  },
  subjectText: {
    fontSize: 14,
    fontWeight: '600',
  },
  teachersList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  teacherCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
    alignItems: 'center',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  teacherInfo: {
    flex: 1,
  },
  teacherName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  teacherSubject: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  teacherDepartment: {
    fontSize: 12,
    marginBottom: 4,
  },
  contactInfo: {
    gap: 2,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  contactText: {
    fontSize: 12,
  },
  actions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 8,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 40,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  cancelButton: {
    fontSize: 16,
  },
  saveButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 16,
  },
});
