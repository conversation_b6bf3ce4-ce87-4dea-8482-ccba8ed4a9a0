import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSelector } from 'react-redux';
import { useRouter } from 'expo-router';
import { useTheme } from '../../contexts/ThemeContext';
import { RootState } from '../../store';

const { width } = Dimensions.get('window');

export default function DashboardScreen() {
  const { theme, toggleTheme } = useTheme();
  const router = useRouter();
  const { stats } = useSelector((state: RootState) => state.pomodoro);
  const { tasks } = useSelector((state: RootState) => state.tasks);
  const { documents } = useSelector((state: RootState) => state.pdfs);
  const { teachers } = useSelector((state: RootState) => state.teachers);
  const { notes } = useSelector((state: RootState) => state.notes);

  const pendingTasks = tasks.filter(task => !task.completed);
  const completedTasks = tasks.filter(task => task.completed);

  const quickActions = [
    { title: 'Start Pomodoro', icon: 'timer', color: theme.colors.primary, route: '/pomodoro' },
    { title: 'Add Task', icon: 'add-circle', color: theme.colors.success, route: '/tasks' },
    { title: 'Upload PDF', icon: 'document-attach', color: theme.colors.warning, route: '/pdfs' },
    { title: 'Create Note', icon: 'create', color: theme.colors.accent, route: '/notes' },
  ];

  const StatCard = ({ title, value, icon, color }: any) => (
    <View style={[styles.statCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}>
      <View style={[styles.statIcon, { backgroundColor: color + '20' }]}>
        <Ionicons name={icon} size={24} color={color} />
      </View>
      <Text style={[styles.statValue, { color: theme.colors.text }]}>{value}</Text>
      <Text style={[styles.statTitle, { color: theme.colors.textSecondary }]}>{title}</Text>
    </View>
  );

  const QuickActionCard = ({ title, icon, color, route }: any) => (
    <TouchableOpacity
      style={[styles.actionCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
      onPress={() => router.push(route)}
    >
      <View style={[styles.actionIcon, { backgroundColor: color + '20' }]}>
        <Ionicons name={icon} size={28} color={color} />
      </View>
      <Text style={[styles.actionTitle, { color: theme.colors.text }]}>{title}</Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={[styles.greeting, { color: theme.colors.text }]}>Good morning!</Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>Ready to study?</Text>
        </View>
        <TouchableOpacity onPress={toggleTheme} style={[styles.themeButton, { backgroundColor: theme.colors.surface }]}>
          <Ionicons
            name={theme.mode === 'dark' ? 'sunny' : 'moon'}
            size={24}
            color={theme.colors.primary}
          />
        </TouchableOpacity>
      </View>

      {/* Stats */}
      <View style={styles.statsContainer}>
        <StatCard
          title="Study Time"
          value={`${Math.floor(stats.totalStudyTime / 60)}h ${stats.totalStudyTime % 60}m`}
          icon="time"
          color={theme.colors.primary}
        />
        <StatCard
          title="Sessions"
          value={stats.sessionsCompleted}
          icon="checkmark-circle"
          color={theme.colors.success}
        />
        <StatCard
          title="Pending Tasks"
          value={pendingTasks.length}
          icon="list"
          color={theme.colors.warning}
        />
        <StatCard
          title="Notes"
          value={notes.length}
          icon="document-text"
          color={theme.colors.accent}
        />
      </View>

      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Quick Actions</Text>
        <View style={styles.actionsContainer}>
          {quickActions.map((action, index) => (
            <QuickActionCard key={index} {...action} />
          ))}
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 50,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  greeting: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 16,
    marginTop: 4,
  },
  themeButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  statCard: {
    width: (width - 60) / 2,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginRight: 20,
    marginBottom: 20,
    alignItems: 'center',
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statTitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  actionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    width: (width - 60) / 2,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    alignItems: 'center',
  },
  actionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  stepContainer: {
    gap: 8,
    marginBottom: 8,
  },
  reactLogo: {
    height: 178,
    width: 290,
    bottom: 0,
    left: 0,
    position: 'absolute',
  },
});
