import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'expo-router';
import { useTheme } from '../../contexts/ThemeContext';
import { RootState } from '../../store';
import { deleteNote, setSearchQuery, setSelectedSubject, setCurrentNote } from '../../store/slices/notesSlice';
import { Note } from '../../types';

export default function NotesScreen() {
  const { theme } = useTheme();
  const dispatch = useDispatch();
  const router = useRouter();
  const { notes, searchQuery, selectedSubject } = useSelector((state: RootState) => state.notes);

  const subjects = ['All', 'Mathematics', 'Physics', 'Chemistry', 'Biology', 'Computer Science', 'English', 'History', 'Other'];

  const filteredNotes = notes.filter(note => {
    const matchesSearch = note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         note.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (note.subject && note.subject.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesSubject = !selectedSubject || selectedSubject === 'All' || note.subject === selectedSubject;
    return matchesSearch && matchesSubject;
  });

  const handleCreateNote = () => {
    dispatch(setCurrentNote(null));
    router.push('/note-editor');
  };

  const handleEditNote = (note: Note) => {
    dispatch(setCurrentNote(note));
    router.push('/note-editor');
  };

  const handleDeleteNote = (noteId: string) => {
    Alert.alert(
      'Delete Note',
      'Are you sure you want to delete this note?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => dispatch(deleteNote(noteId))
        }
      ]
    );
  };

  const SubjectChip = ({ subject }: { subject: string }) => (
    <TouchableOpacity
      style={[
        styles.subjectChip,
        {
          backgroundColor: selectedSubject === subject ? theme.colors.primary : theme.colors.surface,
          borderColor: theme.colors.border,
        }
      ]}
      onPress={() => dispatch(setSelectedSubject(subject === 'All' ? null : subject))}
    >
      <Text style={[
        styles.subjectText,
        { color: selectedSubject === subject ? '#FFFFFF' : theme.colors.text }
      ]}>
        {subject}
      </Text>
    </TouchableOpacity>
  );

  const NoteCard = ({ note }: { note: Note }) => (
    <TouchableOpacity
      style={[styles.noteCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
      onPress={() => handleEditNote(note)}
    >
      <View style={styles.noteHeader}>
        <Text style={[styles.noteTitle, { color: theme.colors.text }]} numberOfLines={1}>
          {note.title}
        </Text>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeleteNote(note.id)}
        >
          <Ionicons name="trash-outline" size={20} color={theme.colors.error} />
        </TouchableOpacity>
      </View>
      
      <Text style={[styles.noteContent, { color: theme.colors.textSecondary }]} numberOfLines={3}>
        {note.content}
      </Text>

      <View style={styles.noteMeta}>
        {note.subject && (
          <View style={[styles.subjectBadge, { backgroundColor: theme.colors.primary + '20' }]}>
            <Text style={[styles.subjectBadgeText, { color: theme.colors.primary }]}>
              {note.subject}
            </Text>
          </View>
        )}
        <Text style={[styles.noteDate, { color: theme.colors.textSecondary }]}>
          {new Date(note.dateModified).toLocaleDateString()}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Notes</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleCreateNote}
        >
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <View style={[styles.searchBox, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}>
          <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            placeholder="Search notes..."
            placeholderTextColor={theme.colors.textSecondary}
            value={searchQuery}
            onChangeText={(text) => dispatch(setSearchQuery(text))}
          />
        </View>
      </View>

      {/* Subjects */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.subjectsContainer}>
        {subjects.map((subject) => (
          <SubjectChip key={subject} subject={subject} />
        ))}
      </ScrollView>

      {/* Notes List */}
      <ScrollView style={styles.notesList}>
        {filteredNotes.map((note) => (
          <NoteCard key={note.id} note={note} />
        ))}
        {filteredNotes.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="document-text-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
              {notes.length === 0 ? 'No notes yet' : 'No notes match your search'}
            </Text>
            <Text style={[styles.emptySubtext, { color: theme.colors.textSecondary }]}>
              {notes.length === 0 ? 'Tap the + button to create your first note' : 'Try adjusting your search or filters'}
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 50,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  subjectsContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  subjectChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 12,
  },
  subjectText: {
    fontSize: 14,
    fontWeight: '600',
  },
  notesList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  noteCard: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
  },
  noteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  noteTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  deleteButton: {
    padding: 4,
  },
  noteContent: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  noteMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  subjectBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  subjectBadgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  noteDate: {
    fontSize: 12,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 40,
  },
});
