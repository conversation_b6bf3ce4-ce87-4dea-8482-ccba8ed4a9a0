import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'expo-router';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import { useTheme } from '../../contexts/ThemeContext';
import { RootState } from '../../store';
import { addDocument, setSearchQuery, setSelectedCategory, setCurrentDocument } from '../../store/slices/pdfsSlice';
import { PDFDocument } from '../../types';

export default function PDFsScreen() {
  const { theme } = useTheme();
  const dispatch = useDispatch();
  const router = useRouter();
  const { documents, searchQuery, selectedCategory } = useSelector((state: RootState) => state.pdfs);
  const [showAddModal, setShowAddModal] = useState(false);

  const categories = ['All', 'Textbooks', 'Notes', 'Assignments', 'Research', 'Other'];

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         doc.subject?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !selectedCategory || selectedCategory === 'All' || doc.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handlePickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: 'application/pdf',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const newDocument: PDFDocument = {
          id: Date.now().toString(),
          name: asset.name,
          uri: asset.uri,
          category: 'Other',
          dateAdded: new Date(),
          size: asset.size || 0,
          bookmarks: [],
          annotations: [],
        };

        dispatch(addDocument(newDocument));
        Alert.alert('Success', 'PDF added successfully!');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick document');
    }
  };

  const CategoryChip = ({ category }: { category: string }) => (
    <TouchableOpacity
      style={[
        styles.categoryChip,
        {
          backgroundColor: selectedCategory === category ? theme.colors.primary : theme.colors.surface,
          borderColor: theme.colors.border,
        }
      ]}
      onPress={() => dispatch(setSelectedCategory(category === 'All' ? null : category))}
    >
      <Text style={[
        styles.categoryText,
        { color: selectedCategory === category ? '#FFFFFF' : theme.colors.text }
      ]}>
        {category}
      </Text>
    </TouchableOpacity>
  );

  const handleOpenPDF = (document: PDFDocument) => {
    dispatch(setCurrentDocument(document));
    router.push('/pdf-viewer');
  };

  const PDFCard = ({ document }: { document: PDFDocument }) => (
    <TouchableOpacity
      style={[styles.pdfCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
      onPress={() => handleOpenPDF(document)}
    >
      <View style={styles.pdfIcon}>
        <Ionicons name="document-text" size={32} color={theme.colors.primary} />
      </View>
      <View style={styles.pdfInfo}>
        <Text style={[styles.pdfName, { color: theme.colors.text }]} numberOfLines={2}>
          {document.name}
        </Text>
        <Text style={[styles.pdfCategory, { color: theme.colors.textSecondary }]}>
          {document.category} • {(document.size / 1024 / 1024).toFixed(1)} MB
        </Text>
        <Text style={[styles.pdfDate, { color: theme.colors.textSecondary }]}>
          Added {new Date(document.dateAdded).toLocaleDateString()}
        </Text>
      </View>
      <TouchableOpacity style={styles.moreButton}>
        <Ionicons name="ellipsis-vertical" size={20} color={theme.colors.textSecondary} />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>PDF Library</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
          onPress={handlePickDocument}
        >
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <View style={[styles.searchBox, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}>
          <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            placeholder="Search PDFs..."
            placeholderTextColor={theme.colors.textSecondary}
            value={searchQuery}
            onChangeText={(text) => dispatch(setSearchQuery(text))}
          />
        </View>
      </View>

      {/* Categories */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesContainer}>
        {categories.map((category) => (
          <CategoryChip key={category} category={category} />
        ))}
      </ScrollView>

      {/* PDFs List */}
      <ScrollView style={styles.pdfsList}>
        {filteredDocuments.map((document) => (
          <PDFCard key={document.id} document={document} />
        ))}
        {filteredDocuments.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="document-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
              {documents.length === 0 ? 'No PDFs yet' : 'No PDFs match your search'}
            </Text>
            <Text style={[styles.emptySubtext, { color: theme.colors.textSecondary }]}>
              {documents.length === 0 ? 'Tap the + button to add your first PDF' : 'Try adjusting your search or filters'}
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 50,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  categoriesContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  categoryChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 12,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '600',
  },
  pdfsList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  pdfCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
    alignItems: 'center',
  },
  pdfIcon: {
    marginRight: 12,
  },
  pdfInfo: {
    flex: 1,
  },
  pdfName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  pdfCategory: {
    fontSize: 14,
    marginBottom: 2,
  },
  pdfDate: {
    fontSize: 12,
  },
  moreButton: {
    padding: 8,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 40,
  },
});
