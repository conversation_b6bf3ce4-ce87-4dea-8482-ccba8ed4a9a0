import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'expo-router';
import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';
import { useTheme } from '../contexts/ThemeContext';
import { RootState } from '../store';
import { 
  authenticate, 
  setPin, 
  setBiometric, 
  incrementFailedAttempts, 
  resetFailedAttempts,
  checkBlockStatus 
} from '../store/slices/authSlice';

const { width } = Dimensions.get('window');

export default function AuthScreen() {
  const { theme } = useTheme();
  const dispatch = useDispatch();
  const router = useRouter();
  const { hasPin, biometricEnabled, failedAttempts, isBlocked, blockUntil } = useSelector(
    (state: RootState) => state.auth
  );

  const [pin, setCurrentPin] = useState('');
  const [isSettingPin, setIsSettingPin] = useState(!hasPin);
  const [confirmPin, setConfirmPin] = useState('');
  const [step, setStep] = useState<'enter' | 'confirm'>('enter');
  const [biometricAvailable, setBiometricAvailable] = useState(false);

  useEffect(() => {
    checkBiometricAvailability();
    dispatch(checkBlockStatus());
  }, []);

  const checkBiometricAvailability = async () => {
    const hasHardware = await LocalAuthentication.hasHardwareAsync();
    const isEnrolled = await LocalAuthentication.isEnrolledAsync();
    setBiometricAvailable(hasHardware && isEnrolled);
  };

  const handleBiometricAuth = async () => {
    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to access your study app',
        fallbackLabel: 'Use PIN',
      });

      if (result.success) {
        dispatch(authenticate());
        dispatch(resetFailedAttempts());
        router.replace('/(tabs)');
      }
    } catch (error) {
      console.error('Biometric authentication error:', error);
    }
  };

  const handlePinInput = (digit: string) => {
    if (pin.length < 4) {
      setCurrentPin(pin + digit);
    }
  };

  const handlePinDelete = () => {
    setCurrentPin(pin.slice(0, -1));
  };

  const handlePinSubmit = async () => {
    if (pin.length !== 4) return;

    if (isSettingPin) {
      if (step === 'enter') {
        setConfirmPin(pin);
        setCurrentPin('');
        setStep('confirm');
      } else {
        if (pin === confirmPin) {
          await SecureStore.setItemAsync('user_pin', pin);
          dispatch(setPin(true));
          setIsSettingPin(false);
          Alert.alert('Success', 'PIN set successfully!');
        } else {
          Alert.alert('Error', 'PINs do not match. Please try again.');
          setCurrentPin('');
          setConfirmPin('');
          setStep('enter');
        }
      }
    } else {
      try {
        const storedPin = await SecureStore.getItemAsync('user_pin');
        if (pin === storedPin) {
          dispatch(authenticate());
          dispatch(resetFailedAttempts());
          router.replace('/(tabs)');
        } else {
          dispatch(incrementFailedAttempts());
          setCurrentPin('');
          Alert.alert('Error', 'Incorrect PIN. Please try again.');
        }
      } catch (error) {
        Alert.alert('Error', 'Failed to verify PIN');
      }
    }
  };

  const enableBiometric = async () => {
    if (biometricAvailable) {
      dispatch(setBiometric(true));
      Alert.alert('Success', 'Biometric authentication enabled!');
    }
  };

  const PinDot = ({ filled }: { filled: boolean }) => (
    <View style={[
      styles.pinDot,
      {
        backgroundColor: filled ? theme.colors.primary : 'transparent',
        borderColor: theme.colors.primary,
      }
    ]} />
  );

  const NumberButton = ({ number, onPress }: { number: string; onPress: () => void }) => (
    <TouchableOpacity
      style={[styles.numberButton, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
      onPress={onPress}
    >
      <Text style={[styles.numberText, { color: theme.colors.text }]}>{number}</Text>
    </TouchableOpacity>
  );

  if (isBlocked && blockUntil && Date.now() < blockUntil) {
    const remainingTime = Math.ceil((blockUntil - Date.now()) / 1000 / 60);
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.blockedContainer}>
          <Ionicons name="lock-closed" size={64} color={theme.colors.error} />
          <Text style={[styles.blockedTitle, { color: theme.colors.error }]}>App Locked</Text>
          <Text style={[styles.blockedText, { color: theme.colors.textSecondary }]}>
            Too many failed attempts. Please try again in {remainingTime} minutes.
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <Ionicons name="shield-checkmark" size={48} color={theme.colors.primary} />
        <Text style={[styles.title, { color: theme.colors.text }]}>
          {isSettingPin ? 'Set Your PIN' : 'Enter Your PIN'}
        </Text>
        <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
          {isSettingPin 
            ? (step === 'enter' ? 'Create a 4-digit PIN' : 'Confirm your PIN')
            : 'Enter your 4-digit PIN to continue'
          }
        </Text>
      </View>

      <View style={styles.pinContainer}>
        <View style={styles.pinDots}>
          {[0, 1, 2, 3].map((index) => (
            <PinDot key={index} filled={index < pin.length} />
          ))}
        </View>

        {failedAttempts > 0 && (
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            {failedAttempts} failed attempt{failedAttempts > 1 ? 's' : ''}
          </Text>
        )}
      </View>

      <View style={styles.keypad}>
        <View style={styles.keypadRow}>
          <NumberButton number="1" onPress={() => handlePinInput('1')} />
          <NumberButton number="2" onPress={() => handlePinInput('2')} />
          <NumberButton number="3" onPress={() => handlePinInput('3')} />
        </View>
        <View style={styles.keypadRow}>
          <NumberButton number="4" onPress={() => handlePinInput('4')} />
          <NumberButton number="5" onPress={() => handlePinInput('5')} />
          <NumberButton number="6" onPress={() => handlePinInput('6')} />
        </View>
        <View style={styles.keypadRow}>
          <NumberButton number="7" onPress={() => handlePinInput('7')} />
          <NumberButton number="8" onPress={() => handlePinInput('8')} />
          <NumberButton number="9" onPress={() => handlePinInput('9')} />
        </View>
        <View style={styles.keypadRow}>
          {biometricAvailable && !isSettingPin && (
            <TouchableOpacity
              style={[styles.numberButton, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
              onPress={handleBiometricAuth}
            >
              <Ionicons name="finger-print" size={24} color={theme.colors.primary} />
            </TouchableOpacity>
          )}
          {(!biometricAvailable || isSettingPin) && <View style={styles.numberButton} />}
          
          <NumberButton number="0" onPress={() => handlePinInput('0')} />
          
          <TouchableOpacity
            style={[styles.numberButton, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
            onPress={handlePinDelete}
          >
            <Ionicons name="backspace" size={24} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </View>

      {pin.length === 4 && (
        <TouchableOpacity
          style={[styles.submitButton, { backgroundColor: theme.colors.primary }]}
          onPress={handlePinSubmit}
        >
          <Text style={styles.submitButtonText}>
            {isSettingPin ? (step === 'enter' ? 'Continue' : 'Confirm') : 'Unlock'}
          </Text>
        </TouchableOpacity>
      )}

      {isSettingPin && biometricAvailable && step === 'enter' && (
        <TouchableOpacity
          style={[styles.biometricButton, { borderColor: theme.colors.primary }]}
          onPress={enableBiometric}
        >
          <Ionicons name="finger-print" size={24} color={theme.colors.primary} />
          <Text style={[styles.biometricText, { color: theme.colors.primary }]}>
            Enable Biometric Authentication
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 60,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  pinContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  pinDots: {
    flexDirection: 'row',
    gap: 20,
    marginBottom: 20,
  },
  pinDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
  keypad: {
    alignItems: 'center',
    marginBottom: 40,
  },
  keypadRow: {
    flexDirection: 'row',
    gap: 20,
    marginBottom: 20,
  },
  numberButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  numberText: {
    fontSize: 24,
    fontWeight: '600',
  },
  submitButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  biometricButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 1,
    gap: 12,
  },
  biometricText: {
    fontSize: 16,
    fontWeight: '600',
  },
  blockedContainer: {
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  blockedTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 12,
  },
  blockedText: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
});
