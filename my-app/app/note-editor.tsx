import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSelector, useDispatch } from 'react-redux';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useTheme } from '../contexts/ThemeContext';
import { RootState } from '../store';
import { addNote, updateNote } from '../store/slices/notesSlice';
import { Note } from '../types';

export default function NoteEditorScreen() {
  const { theme } = useTheme();
  const dispatch = useDispatch();
  const router = useRouter();
  const { noteId } = useLocalSearchParams();
  const { currentNote } = useSelector((state: RootState) => state.notes);
  
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [subject, setSubject] = useState('');
  const [hasChanges, setHasChanges] = useState(false);

  const isEditing = !!noteId && !!currentNote;

  useEffect(() => {
    if (isEditing && currentNote) {
      setTitle(currentNote.title);
      setContent(currentNote.content);
      setSubject(currentNote.subject || '');
    }
  }, [isEditing, currentNote]);

  useEffect(() => {
    setHasChanges(true);
  }, [title, content, subject]);

  const handleSave = () => {
    if (!title.trim()) {
      Alert.alert('Error', 'Please enter a note title');
      return;
    }

    if (!content.trim()) {
      Alert.alert('Error', 'Please enter some content');
      return;
    }

    if (isEditing && currentNote) {
      const updatedNote: Note = {
        ...currentNote,
        title: title.trim(),
        content: content.trim(),
        subject: subject.trim() || undefined,
        dateModified: new Date(),
      };
      dispatch(updateNote(updatedNote));
      Alert.alert('Success', 'Note updated successfully!');
    } else {
      const newNote: Note = {
        id: Date.now().toString(),
        title: title.trim(),
        content: content.trim(),
        subject: subject.trim() || undefined,
        dateCreated: new Date(),
        dateModified: new Date(),
      };
      dispatch(addNote(newNote));
      Alert.alert('Success', 'Note saved successfully!');
    }

    setHasChanges(false);
    router.back();
  };

  const handleBack = () => {
    if (hasChanges) {
      Alert.alert(
        'Unsaved Changes',
        'You have unsaved changes. Do you want to save before leaving?',
        [
          { text: 'Discard', style: 'destructive', onPress: () => router.back() },
          { text: 'Cancel', style: 'cancel' },
          { text: 'Save', onPress: handleSave },
        ]
      );
    } else {
      router.back();
    }
  };

  return (
    <KeyboardAvoidingView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
        <TouchableOpacity style={styles.headerButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          {isEditing ? 'Edit Note' : 'New Note'}
        </Text>

        <TouchableOpacity
          style={[styles.saveButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleSave}
        >
          <Text style={styles.saveButtonText}>Save</Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Title Input */}
        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Title</Text>
          <TextInput
            style={[
              styles.titleInput,
              {
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border,
                color: theme.colors.text,
              }
            ]}
            value={title}
            onChangeText={setTitle}
            placeholder="Enter note title"
            placeholderTextColor={theme.colors.textSecondary}
            fontSize={18}
            fontWeight="600"
          />
        </View>

        {/* Subject Input */}
        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Subject (Optional)</Text>
          <TextInput
            style={[
              styles.subjectInput,
              {
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border,
                color: theme.colors.text,
              }
            ]}
            value={subject}
            onChangeText={setSubject}
            placeholder="Enter subject"
            placeholderTextColor={theme.colors.textSecondary}
          />
        </View>

        {/* Content Input */}
        <View style={[styles.inputGroup, { flex: 1 }]}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Content</Text>
          <TextInput
            style={[
              styles.contentInput,
              {
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border,
                color: theme.colors.text,
              }
            ]}
            value={content}
            onChangeText={setContent}
            placeholder="Start writing your note..."
            placeholderTextColor={theme.colors.textSecondary}
            multiline
            textAlignVertical="top"
          />
        </View>

        {/* Info */}
        {isEditing && currentNote && (
          <View style={styles.infoContainer}>
            <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
              Created: {new Date(currentNote.dateCreated).toLocaleDateString()} at{' '}
              {new Date(currentNote.dateCreated).toLocaleTimeString()}
            </Text>
            <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
              Modified: {new Date(currentNote.dateModified).toLocaleDateString()} at{' '}
              {new Date(currentNote.dateModified).toLocaleTimeString()}
            </Text>
          </View>
        )}
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 12,
    borderBottomWidth: 1,
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginHorizontal: 16,
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  titleInput: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    fontSize: 18,
    fontWeight: '600',
  },
  subjectInput: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    fontSize: 16,
  },
  contentInput: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    fontSize: 16,
    lineHeight: 24,
  },
  infoContainer: {
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
    marginTop: 16,
  },
  infoText: {
    fontSize: 12,
    marginBottom: 4,
  },
});
