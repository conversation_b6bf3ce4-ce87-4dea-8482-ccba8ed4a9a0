import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput, Modal, Alert, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSelector, useDispatch } from 'react-redux';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { WebView } from 'react-native-webview';
import { useTheme } from '../contexts/ThemeContext';
import { RootState } from '../store';
import { addBookmark, addAnnotation, removeBookmark, removeAnnotation } from '../store/slices/pdfsSlice';
import { PDFBookmark, PDFAnnotation } from '../types';

const { width, height } = Dimensions.get('window');

export default function PDFViewerScreen() {
  const { theme } = useTheme();
  const dispatch = useDispatch();
  const router = useRouter();
  const { documentId } = useLocalSearchParams();
  const { currentDocument } = useSelector((state: RootState) => state.pdfs);
  
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [showBookmarks, setShowBookmarks] = useState(false);
  const [showAnnotations, setShowAnnotations] = useState(false);
  const [showAddBookmark, setShowAddBookmark] = useState(false);
  const [showAddAnnotation, setShowAddAnnotation] = useState(false);
  const [bookmarkTitle, setBookmarkTitle] = useState('');
  const [annotationText, setAnnotationText] = useState('');
  const [annotationPosition, setAnnotationPosition] = useState({ x: 0, y: 0 });
  
  const webViewRef = useRef<WebView>(null);

  if (!currentDocument) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.errorContainer}>
          <Ionicons name="document-outline" size={64} color={theme.colors.textSecondary} />
          <Text style={[styles.errorText, { color: theme.colors.textSecondary }]}>
            Document not found
          </Text>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  const handleAddBookmark = () => {
    if (!bookmarkTitle.trim()) {
      Alert.alert('Error', 'Please enter a bookmark title');
      return;
    }

    const bookmark: PDFBookmark = {
      id: Date.now().toString(),
      page: currentPage,
      title: bookmarkTitle,
      dateCreated: new Date(),
    };

    dispatch(addBookmark({ documentId: currentDocument.id, bookmark }));
    setBookmarkTitle('');
    setShowAddBookmark(false);
    Alert.alert('Success', 'Bookmark added successfully!');
  };

  const handleAddAnnotation = () => {
    if (!annotationText.trim()) {
      Alert.alert('Error', 'Please enter annotation text');
      return;
    }

    const annotation: PDFAnnotation = {
      id: Date.now().toString(),
      page: currentPage,
      text: annotationText,
      x: annotationPosition.x,
      y: annotationPosition.y,
      dateCreated: new Date(),
    };

    dispatch(addAnnotation({ documentId: currentDocument.id, annotation }));
    setAnnotationText('');
    setShowAddAnnotation(false);
    Alert.alert('Success', 'Annotation added successfully!');
  };

  const handleDeleteBookmark = (bookmarkId: string) => {
    Alert.alert(
      'Delete Bookmark',
      'Are you sure you want to delete this bookmark?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => dispatch(removeBookmark({ documentId: currentDocument.id, bookmarkId }))
        }
      ]
    );
  };

  const handleDeleteAnnotation = (annotationId: string) => {
    Alert.alert(
      'Delete Annotation',
      'Are you sure you want to delete this annotation?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => dispatch(removeAnnotation({ documentId: currentDocument.id, annotationId }))
        }
      ]
    );
  };

  const BookmarkItem = ({ bookmark }: { bookmark: PDFBookmark }) => (
    <TouchableOpacity
      style={[styles.listItem, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
      onPress={() => {
        setCurrentPage(bookmark.page);
        setShowBookmarks(false);
      }}
    >
      <View style={styles.listItemContent}>
        <Text style={[styles.listItemTitle, { color: theme.colors.text }]}>{bookmark.title}</Text>
        <Text style={[styles.listItemSubtitle, { color: theme.colors.textSecondary }]}>
          Page {bookmark.page} • {new Date(bookmark.dateCreated).toLocaleDateString()}
        </Text>
      </View>
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => handleDeleteBookmark(bookmark.id)}
      >
        <Ionicons name="trash" size={20} color={theme.colors.error} />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  const AnnotationItem = ({ annotation }: { annotation: PDFAnnotation }) => (
    <TouchableOpacity
      style={[styles.listItem, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
      onPress={() => {
        setCurrentPage(annotation.page);
        setShowAnnotations(false);
      }}
    >
      <View style={styles.listItemContent}>
        <Text style={[styles.listItemTitle, { color: theme.colors.text }]} numberOfLines={2}>
          {annotation.text}
        </Text>
        <Text style={[styles.listItemSubtitle, { color: theme.colors.textSecondary }]}>
          Page {annotation.page} • {new Date(annotation.dateCreated).toLocaleDateString()}
        </Text>
      </View>
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => handleDeleteAnnotation(annotation.id)}
      >
        <Ionicons name="trash" size={20} color={theme.colors.error} />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
        <TouchableOpacity style={styles.headerButton} onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        
        <View style={styles.headerTitle}>
          <Text style={[styles.documentTitle, { color: theme.colors.text }]} numberOfLines={1}>
            {currentDocument.name}
          </Text>
          <Text style={[styles.pageInfo, { color: theme.colors.textSecondary }]}>
            Page {currentPage} of {totalPages}
          </Text>
        </View>

        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowBookmarks(true)}
          >
            <Ionicons name="bookmark" size={24} color={theme.colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowAnnotations(true)}
          >
            <Ionicons name="chatbox" size={24} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* PDF Viewer */}
      <View style={styles.pdfContainer}>
        <WebView
          ref={webViewRef}
          source={{ uri: currentDocument.uri }}
          style={styles.webView}
          onLoadEnd={() => {
            // In a real implementation, you would get the total pages from the PDF
            setTotalPages(10); // Placeholder
          }}
        />
      </View>

      {/* Bottom Toolbar */}
      <View style={[styles.toolbar, { backgroundColor: theme.colors.surface, borderTopColor: theme.colors.border }]}>
        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={() => setCurrentPage(Math.max(1, currentPage - 1))}
          disabled={currentPage <= 1}
        >
          <Ionicons 
            name="chevron-back" 
            size={24} 
            color={currentPage <= 1 ? theme.colors.textSecondary : theme.colors.primary} 
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => setShowAddBookmark(true)}
        >
          <Ionicons name="bookmark-outline" size={20} color="#FFFFFF" />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.colors.success }]}
          onPress={() => setShowAddAnnotation(true)}
        >
          <Ionicons name="add" size={20} color="#FFFFFF" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage >= totalPages}
        >
          <Ionicons 
            name="chevron-forward" 
            size={24} 
            color={currentPage >= totalPages ? theme.colors.textSecondary : theme.colors.primary} 
          />
        </TouchableOpacity>
      </View>

      {/* Bookmarks Modal */}
      <Modal visible={showBookmarks} animationType="slide" presentationStyle="pageSheet">
        <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowBookmarks(false)}>
              <Text style={[styles.modalCancel, { color: theme.colors.primary }]}>Close</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Bookmarks</Text>
            <View style={{ width: 50 }} />
          </View>
          
          <View style={styles.modalContent}>
            {currentDocument.bookmarks.map((bookmark) => (
              <BookmarkItem key={bookmark.id} bookmark={bookmark} />
            ))}
            {currentDocument.bookmarks.length === 0 && (
              <View style={styles.emptyState}>
                <Ionicons name="bookmark-outline" size={48} color={theme.colors.textSecondary} />
                <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
                  No bookmarks yet
                </Text>
              </View>
            )}
          </View>
        </View>
      </Modal>

      {/* Annotations Modal */}
      <Modal visible={showAnnotations} animationType="slide" presentationStyle="pageSheet">
        <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAnnotations(false)}>
              <Text style={[styles.modalCancel, { color: theme.colors.primary }]}>Close</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Annotations</Text>
            <View style={{ width: 50 }} />
          </View>
          
          <View style={styles.modalContent}>
            {currentDocument.annotations.map((annotation) => (
              <AnnotationItem key={annotation.id} annotation={annotation} />
            ))}
            {currentDocument.annotations.length === 0 && (
              <View style={styles.emptyState}>
                <Ionicons name="chatbox-outline" size={48} color={theme.colors.textSecondary} />
                <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
                  No annotations yet
                </Text>
              </View>
            )}
          </View>
        </View>
      </Modal>

      {/* Add Bookmark Modal */}
      <Modal visible={showAddBookmark} animationType="fade" transparent>
        <View style={styles.overlayModal}>
          <View style={[styles.overlayContent, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.overlayTitle, { color: theme.colors.text }]}>Add Bookmark</Text>
            <TextInput
              style={[styles.overlayInput, { backgroundColor: theme.colors.background, borderColor: theme.colors.border, color: theme.colors.text }]}
              value={bookmarkTitle}
              onChangeText={setBookmarkTitle}
              placeholder="Enter bookmark title"
              placeholderTextColor={theme.colors.textSecondary}
              autoFocus
            />
            <View style={styles.overlayButtons}>
              <TouchableOpacity
                style={[styles.overlayButton, { backgroundColor: theme.colors.textSecondary }]}
                onPress={() => setShowAddBookmark(false)}
              >
                <Text style={styles.overlayButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.overlayButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleAddBookmark}
              >
                <Text style={styles.overlayButtonText}>Add</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Add Annotation Modal */}
      <Modal visible={showAddAnnotation} animationType="fade" transparent>
        <View style={styles.overlayModal}>
          <View style={[styles.overlayContent, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.overlayTitle, { color: theme.colors.text }]}>Add Annotation</Text>
            <TextInput
              style={[styles.overlayTextArea, { backgroundColor: theme.colors.background, borderColor: theme.colors.border, color: theme.colors.text }]}
              value={annotationText}
              onChangeText={setAnnotationText}
              placeholder="Enter annotation text"
              placeholderTextColor={theme.colors.textSecondary}
              multiline
              numberOfLines={4}
              autoFocus
            />
            <View style={styles.overlayButtons}>
              <TouchableOpacity
                style={[styles.overlayButton, { backgroundColor: theme.colors.textSecondary }]}
                onPress={() => setShowAddAnnotation(false)}
              >
                <Text style={styles.overlayButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.overlayButton, { backgroundColor: theme.colors.success }]}
                onPress={handleAddAnnotation}
              >
                <Text style={styles.overlayButtonText}>Add</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 12,
    borderBottomWidth: 1,
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    marginHorizontal: 16,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  pageInfo: {
    fontSize: 12,
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  pdfContainer: {
    flex: 1,
  },
  webView: {
    flex: 1,
  },
  toolbar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderTopWidth: 1,
  },
  toolbarButton: {
    padding: 8,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorText: {
    fontSize: 18,
    marginTop: 16,
    marginBottom: 24,
    textAlign: 'center',
  },
  backButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalCancel: {
    fontSize: 16,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
  },
  listItemContent: {
    flex: 1,
  },
  listItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  listItemSubtitle: {
    fontSize: 12,
  },
  deleteButton: {
    padding: 8,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 12,
  },
  overlayModal: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  overlayContent: {
    width: '100%',
    maxWidth: 400,
    padding: 24,
    borderRadius: 16,
  },
  overlayTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  overlayInput: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 16,
    marginBottom: 20,
  },
  overlayTextArea: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 16,
    marginBottom: 20,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  overlayButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  overlayButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  overlayButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
