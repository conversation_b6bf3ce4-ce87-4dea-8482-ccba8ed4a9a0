import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Provider } from 'react-redux';
import 'react-native-reanimated';

import { store } from '../store';
import { ThemeProvider } from '../contexts/ThemeContext';
import AuthWrapper from '../components/AuthWrapper';
import DataPersistenceProvider from '../components/DataPersistenceProvider';

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <Provider store={store}>
      <ThemeProvider>
        <DataPersistenceProvider>
          <AuthWrapper>
            <Stack>
              <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
              <Stack.Screen name="auth" options={{ headerShown: false }} />
              <Stack.Screen name="pdf-viewer" options={{ headerShown: false }} />
              <Stack.Screen name="note-editor" options={{ headerShown: false }} />
              <Stack.Screen name="+not-found" />
            </Stack>
            <StatusBar style="auto" />
          </AuthWrapper>
        </DataPersistenceProvider>
      </ThemeProvider>
    </Provider>
  );
}
