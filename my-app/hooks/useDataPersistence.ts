import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { 
  teachersStorage, 
  pdfsStorage, 
  tasksStorage, 
  notesStorage, 
  pomodoroStorage,
  pinStorage 
} from '../utils/secureStorage';
import { setTeachers } from '../store/slices/teachersSlice';
import { setDocuments } from '../store/slices/pdfsSlice';
import { setTasks } from '../store/slices/tasksSlice';
import { setNotes } from '../store/slices/notesSlice';
import { setSessions, updateStats, updateSettings } from '../store/slices/pomodoroSlice';
import { setPin } from '../store/slices/authSlice';

export const useDataPersistence = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    loadPersistedData();
  }, []);

  const loadPersistedData = async () => {
    try {
      // Load authentication state
      const hasPin = await pinStorage.exists();
      dispatch(setPin(hasPin));

      // Load teachers data
      const teachers = await teachersStorage.load();
      if (teachers && Array.isArray(teachers)) {
        dispatch(setTeachers(teachers));
      }

      // Load PDFs data
      const pdfs = await pdfsStorage.load();
      if (pdfs && Array.isArray(pdfs)) {
        dispatch(setDocuments(pdfs));
      }

      // Load tasks data
      const tasks = await tasksStorage.load();
      if (tasks && Array.isArray(tasks)) {
        dispatch(setTasks(tasks));
      }

      // Load notes data
      const notes = await notesStorage.load();
      if (notes && Array.isArray(notes)) {
        dispatch(setNotes(notes));
      }

      // Load pomodoro data
      const pomodoroData = await pomodoroStorage.load();
      if (pomodoroData) {
        if (pomodoroData.sessions && Array.isArray(pomodoroData.sessions)) {
          dispatch(setSessions(pomodoroData.sessions));
        }
        if (pomodoroData.stats) {
          dispatch(updateStats(pomodoroData.stats));
        }
        if (pomodoroData.settings) {
          dispatch(updateSettings(pomodoroData.settings));
        }
      }

    } catch (error) {
      console.error('Error loading persisted data:', error);
    }
  };

  return { loadPersistedData };
};
