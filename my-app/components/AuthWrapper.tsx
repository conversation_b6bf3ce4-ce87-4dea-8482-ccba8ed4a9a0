import React, { useEffect, ReactNode } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'expo-router';
import { AppState, AppStateStatus } from 'react-native';
import { RootState } from '../store';
import { lock, updateLastActiveTime, checkBlockStatus } from '../store/slices/authSlice';

interface AuthWrapperProps {
  children: ReactNode;
}

const AuthWrapper: React.FC<AuthWrapperProps> = ({ children }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { isAuthenticated, isLocked, hasPin, lockTimeout, lastActiveTime } = useSelector(
    (state: RootState) => state.auth
  );

  useEffect(() => {
    // Check if app should be locked on startup
    if (hasPin && !isAuthenticated) {
      router.replace('/auth');
    }

    // Check block status on startup
    dispatch(checkBlockStatus());
  }, []);

  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        // App came to foreground
        dispatch(updateLastActiveTime());
        dispatch(checkBlockStatus());
        
        // Check if app should be locked due to timeout
        if (hasPin && isAuthenticated) {
          const timeSinceLastActive = Date.now() - lastActiveTime;
          const timeoutMs = lockTimeout * 60 * 1000;
          
          if (timeSinceLastActive > timeoutMs) {
            dispatch(lock());
            router.replace('/auth');
          }
        }
      } else if (nextAppState === 'background' || nextAppState === 'inactive') {
        // App went to background
        dispatch(updateLastActiveTime());
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [hasPin, isAuthenticated, lockTimeout, lastActiveTime]);

  useEffect(() => {
    // Redirect to auth if locked
    if (hasPin && (isLocked || !isAuthenticated)) {
      router.replace('/auth');
    }
  }, [isLocked, isAuthenticated, hasPin]);

  return <>{children}</>;
};

export default AuthWrapper;
